<template>
  <module edit>
    <template #main>
      <a-form
        ref="formRef"
        layout="horizontal"
        class="general-form"
        :model="entity"
      >
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item
                  field="type"
                  :label="t('material.form.type')"
                  :rules="[{ required: true, message: t('material.form.type_required') }]"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-radio-group v-model="entity.type">
                    <a-radio value="image">{{t('material.tabs.image')}}</a-radio>
                    <a-radio value="audio">{{t('material.tabs.audio')}}</a-radio>
                    <a-radio value="video">{{t('material.tabs.video')}}</a-radio>
                    <a-radio value="file">{{t('material.tabs.file')}}</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item
                  field="name"
                  :label="t('material.form.name')"
                  :rules="[
                    { required: true, message: t('material.form.name_required') },
                    { max: 50, message: '素材名称不能超过50个字符' }
                  ]"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-input
                    v-model="entity.name"
                    :placeholder="t('material.form.name_placeholder')"
                    :max-length="50"
                    show-word-limit
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="80" v-if="entity.type === 'video'">
              <a-col :span="24">
                <a-form-item
                  field="videoTitle"
                  label="视频标题"
                  :rules="[
                    { required: true, message: '请输入视频标题' },
                    { max: 64, message: '视频标题不能超过64个字符' }
                  ]"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-input
                    v-model="entity.videoTitle"
                    placeholder="请输入视频标题"
                    :max-length="64"
                    show-word-limit
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item
                  field="file"
                  :label="t('material.form.upload_file')"
                  :rules="[{ required: true, message: t('material.form.file_required') }]"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-upload
                    ref="uploadRef"
                    :file-list="fileList"
                    :show-file-list="false"
                    :auto-upload="false"
                    :multiple="false"
                    :limit="1"
                    :accept="getAcceptTypes()"
                    @change="handleFileChange"
                  >
                    <template #upload-button>
                      <div class="upload-area">
                        <div class="upload-icon">
                          <icon-cloud-upload />
                        </div>
                        <div class="upload-text">
                          {{t('material.form.click_or_drag_upload')}}
                        </div>
                      </div>
                    </template>
                  </a-upload>
                  
                  <!-- 自定义文件列表显示 -->
                  <div v-if="fileList.length > 0" class="custom-file-list">
                    <div v-for="file in fileList" :key="file.uid" class="custom-file-item">
                      <div class="file-info">
                        <icon-file class="file-icon" />
                        <span class="file-name" @click="previewFile(file)">{{ file.name }}</span>
                        <span class="file-size">({{ formatFileSize(file.size) }})</span>
                      </div>
                      <div class="file-actions">
                        <a-button 
                          type="text" 
                          size="small" 
                          @click="removeFile(file)"
                          class="remove-btn"
                        >
                          <icon-delete />
                        </a-button>
                      </div>
                    </div>
                  </div>
                </a-form-item>
                <div v-if="entity.type" class="file-format-tip">
                  {{getFileFormatDescription()}}
                </div>
              </a-col>
            </a-row>
            <a-row :gutter="80" v-if="entity.type === 'video'">
              <a-col :span="24">
                <a-form-item
                  field="videoDescription"
                  label="视频描述"
                  :rules="[
                    { required: true, message: '请输入视频描述' },
                    { max: 200, message: '视频描述不能超过200个字符' }
                  ]"
                  :label-col-props="{ span: 4 }"
                  :wrapper-col-props="{ span: 20 }"
                >
                  <a-textarea
                    v-model="entity.videoDescription"
                    placeholder="请输入视频描述"
                    :max-length="200"
                    show-word-limit
                    :auto-size="{ minRows: 3, maxRows: 5 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>

          </a-card>
        </a-space>
      </a-form>
    </template>
  </module>
</template>

<script>
import { ref, provide, getCurrentInstance, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Message } from "@arco-design/web-vue";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();

    const module = ref({
      entityIdField: "id",
      mainPath: "/material/material",
      breadcrumb: [
        {
          name: t('material.title'),
          path: "/material/material"
        },
        {
          name: t('material.button.upload')
        }
      ],
      isEdit: false
    });

    const loading = ref(false);
    const entity = ref({
      name: "",
      type: "image", // 默认选中图片
      file: null,
      videoTitle: "", // 视频标题
      videoDescription: "" // 视频描述
    });
    const formRef = ref({});
    const uploadRef = ref();
    const fileList = ref([]);

    const stopWatch = watch(() => entity.value.type, (newType, oldType) => {
       if (newType !== oldType) {
         // 重置全部表单字段并清除校验状态
         formRef.value.clearValidate();
         entity.value.name = '';
         entity.value.videoTitle = '';
         entity.value.videoDescription = '';
         entity.value.file = null;
         fileList.value = [];
       }
    });

    // 文件格式配置
    const fileConfig = {
      image: {
        formats: ['png', 'jpeg', 'jpg', 'gif'],
        maxSize: 10 * 1024 * 1024, // 10MB
        description: '支持PNG、JPEG、JPG、GIF格式，图片文件不超过10MB'
      },
      audio: {
        formats: ['amr', 'mp3'],
        maxSize: 2 * 1024 * 1024, // 2MB
        maxDuration: 60, // 60秒
        description: '支持AMR、MP3格式，音频文件大小不超过2MB，播放长度不超过60s'
      },
      video: {
        formats: ['mp4'],
        maxSize: 10 * 1024 * 1024, // 10MB
        description: '支持MP4格式，视频文件不超过10MB'
      },
      file: {
        formats: ['txt', 'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'xml', 'jpg', 'jpeg', 'png', 'bmp', 'gif'],
        maxSize: 20 * 1024 * 1024, // 20MB
        description: '支持txt、pdf、doc、docx、ppt、pptx、xls、xlsx、xml、jpg、jpeg、png、bmp、gif格式，文件大小不超过20MB'
      }
    };

    // 获取accept类型字符串
    const getAcceptTypes = () => {
      if (!entity.value.type) return '';
      const config = fileConfig[entity.value.type];
      if (!config) return '';
      
      // 将文件扩展名转换为MIME类型或保持为扩展名格式
      const mimeTypes = {
        'png': 'image/png',
        'jpeg': 'image/jpeg',
        'jpg': 'image/jpeg',
        'gif': 'image/gif',
        'mp3': 'audio/mpeg',
        'mp4': 'video/mp4',
        'pdf': 'application/pdf',
        'txt': 'text/plain'
      };
      
      // 优先使用MIME类型，如果没有则使用文件扩展名格式
      const acceptTypes = config.formats.map(format => {
        return mimeTypes[format] || `.${format}`;
      });
      
      return acceptTypes.join(',');
    };

    // 获取文件格式提示
    const getFileFormatTip = () => {
      if (!entity.value.type) return t('material.form.upload_tip');
      return fileConfig[entity.value.type]?.description || t('material.form.upload_tip');
    };

    // 获取文件格式描述
    const getFileFormatDescription = () => {
      if (!entity.value.type) return '';
      return fileConfig[entity.value.type]?.description || '';
    };

    // 验证文件格式和大小
    const validateFile = (file) => {
      if (!entity.value.type) {
        Message.error('请先选择素材类型');
        return false;
      }

      const config = fileConfig[entity.value.type];
      if (!config) return false;

      // 获取文件扩展名
      const fileName = file.name.toLowerCase();
      const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1);

      // 验证文件格式
      if (!config.formats.includes(fileExtension)) {
        Message.error(`不支持的文件格式，${config.description}`);
        return false;
      }

      // 验证文件大小
      if (file.size > config.maxSize) {
        const maxSizeMB = Math.round(config.maxSize / (1024 * 1024));
        Message.error(`文件大小超过限制，最大支持${maxSizeMB}MB`);
        return false;
      }

      // 音频文件需要验证时长（这里只是示例，实际需要读取音频文件）
      if (entity.value.type === 'audio' && config.maxDuration) {
        // 实际项目中需要使用音频API来获取时长
        // 这里只是示例提示
        console.log(`音频文件时长不能超过${config.maxDuration}秒`);
      }

      return true;
    };

    const handleFileChange = (fileListValue, fileItem) => {
      // 处理文件删除情况
      if (fileListValue.length === 0) {
        entity.value.file = null;
        fileList.value = [];
        return;
      }

      if (fileItem && fileItem.file) {
        // 验证文件
        if (!validateFile(fileItem.file)) {
          // 清除文件列表
          uploadRef.value?.clearFiles();
          return;
        }

        entity.value.file = fileItem.file;
        // 更新fileList用于显示
        fileList.value = [fileItem.file];
        // 直接将文件名填入素材名称
        const fileName = fileItem.file.name;
        const lastDotIndex = fileName.lastIndexOf('.');
        entity.value.name = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
        
        // 自动识别文件类型（如果用户选择的是默认的图片类型，则根据文件类型自动切换）
        const fileType = fileItem.file.type;
        if (fileType.startsWith('video/') && entity.value.type === 'image') {
          entity.value.type = 'video';
        } else if (fileType.startsWith('audio/') && entity.value.type === 'image') {
          entity.value.type = 'audio';
        } else if (!fileType.startsWith('image/') && !fileType.startsWith('video/') && !fileType.startsWith('audio/') && entity.value.type === 'image') {
          entity.value.type = 'file';
        }
      }
    };

    // 格式化文件大小
    const formatFileSize = (size) => {
      if (!size) return '0B';
      if (size < 1024) {
        return size + 'B';
      } else if (size < 1024 * 1024) {
        return Math.round(size / 1024) + 'KB';
      } else {
        return Math.round(size / (1024 * 1024)) + 'MB';
      }
    };

    // 删除文件
    const removeFile = (file) => {
      fileList.value = fileList.value.filter(item => item.uid !== file.uid);
      entity.value.file = null;
      entity.value.name = '';
    };

    // 添加预览文件功能
    const previewFile = (file) => {
      if (file) {
        const url = URL.createObjectURL(file);
        window.open(url, '_blank');
      } else {
        Message.info('该文件类型不支持预览');
      }
    };

    const bindData = async () => {
      // 上传页面不需要加载数据
    };

    const quit = () => {
      router.push({ path: module.value.mainPath });
    };

    const save = async () => {
      // 添加文件存在性验证
      if (!entity.value.file) {
        Message.error(t('material.form.file_required'));
        return;
      }

      const valid = await formRef.value.validate();
      if (valid) return;
      
      loading.value = true;
      try {
        // 这里应该调用上传API
        const formData = new FormData();
        formData.append('name', entity.value.name);
        formData.append('type', entity.value.type);
        formData.append('file', entity.value.file);
        
        // 如果是视频类型，添加视频标题和描述
        if (entity.value.type === 'video') {
          formData.append('videoTitle', entity.value.videoTitle);
          formData.append('videoDescription', entity.value.videoDescription);
        }
        
        // await uploadMaterial(formData);
        await new Promise(resolve => setTimeout(resolve, 2000));
        Message.success(t('material.message.upload_success'));
        quit();
      } catch (error) {
        Message.error(t('material.message.upload_failed'));
      } finally {
        loading.value = false;
      }
    };

    const setup = {
      t,
      save,
      route,
      router,
      module,
      entity,
      formRef,
      bindData,
      loading,
      uploadRef,
      fileList,
      handleFileChange,
      getFileFormatTip,
      getFileFormatDescription,
      getAcceptTypes,
      formatFileSize,
      removeFile,
      previewFile  // 添加previewFile到setup中
    };
    
    provide("edit", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped>
// 让所有表单标签左对齐且不换行
:deep(.arco-form-item-label) {
  text-align: left !important;
  white-space: nowrap !important;
}

// 让表单内容左对齐
:deep(.arco-form-item-content) {
  text-align: left !important;
}

// 让所有输入框内容左对齐
:deep(.arco-input),
:deep(.arco-textarea),
:deep(.arco-radio-group) {
  text-align: left !important;
}

// 让上传区域内容左对齐
:deep(.arco-upload) {
  text-align: left !important;
}

.upload-area {
  width: 100%;
  height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
  background-color: #fafafa;
  
  &:hover {
    border-color: #1890ff;
    background-color: #f0f8ff;
  }
  
  .upload-icon {
    font-size: 48px;
    color: #1890ff;
    margin-bottom: 16px;
  }
  
  .upload-text {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
  }
}

.file-format-tip {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
  padding-left: 16.666667%; // 与表单内容区域左对齐 (4/24 * 100%)
}

// 简化自定义文件列表样式
.custom-file-list {
  margin-top: 0px;
  
  .custom-file-item {
    display: flex;
    align-items: center;
    padding: 0px 10px;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    background-color: #f1f1f1;
    
    .file-info {
      display: flex;
      align-items: center;
      flex: 1;
      
      .file-icon {
        font-size: 16px;
        color: #1890ff;
        margin-right: 8px;
      }
      
      .file-name {
        font-size: 14px;
        color: #1d2129;
        margin-right: 8px;
        cursor: pointer;
        text-decoration: underline;
        
        &:hover {
          color: #1890ff;
        }
      }
      
      .file-size {
        font-size: 12px;
        color: #86909c;
      }
    }
    
    .file-actions {
      .remove-btn {
        margin-left: 4px;
        color: #f53f3f;
        
        // 调整悬停样式，减小影响范围
        &:hover {
          // background-color: #fee !important;
          color: #f53f3f !important;
          border-radius: 2px;
          transform: scale(1.1);
        }
      }
    }
  }
}
</style>