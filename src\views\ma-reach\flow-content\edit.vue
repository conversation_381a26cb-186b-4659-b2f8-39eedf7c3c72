<template>
  <module edit>
    <template #main>
      <div class="template-body">
        <a-form ref="formRef" layout="vertical" class="left-form" :model="entity">
          <a-space direction="vertical" :size="16">
            <a-card class="general-card">
              <a-row :gutter="24">
                <a-col :span="7">
                  <a-form-item :label="t('reach.sms.communicationId')" field="id">
                    <a-input v-model="entity.id" :placeholder="t('reach.reminder.communicationId')"
                      :disabled="module.isEdit" />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item :label="t('reach.sms.communicationName')" field="name"
                    :rules="[{ required: true, message: t('reach.reminder.communicationName') }]">
                    <a-input v-model="entity.name" :placeholder="t('reach.reminder.communicationName')" />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item :label="t('reach.sms.communicationGroup')" field="group">
                    <a-input v-model="entity.group" :placeholder="t('reach.reminder.communicationGroup')" />
                  </a-form-item>
                </a-col>
                <a-col :span="2">
                  <a-form-item :label="t('reach.sms.status')">
                    <a-switch v-model="entity.enabled" type="round">
                      <template #checked> {{ t('global.button.enable') }} </template>
                      <template #unchecked> {{ t('global.button.disable') }} </template>
                    </a-switch>
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item :label="t('reach.sms.template')" field="channelType">
                    <a-select v-model="entity.flowNodeId" :placeholder="t('reach.reminder.touchpoint')"
                      @change="getFLowList">
                      <a-option value="sms">三方短信</a-option>
                      <a-option value="wechat_enterprise">企业微信消息</a-option>
                      <a-option value="wechat_group">微信群发消息</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item v-if="entity.flowNodeId" field="channelType">
                    <div class="m-t-p">
                      <a-select v-model="entity.flowTemplateId" allow-search :placeholder="t('reach.reminder.template')"
                        @change="changeTemplate">
                        <a-option v-for="item in templateList" :key="item.id" :value="item.id"
                          :label="item.name + ' [' + item.id + ']'" />
                      </a-select>
                    </div>
                  </a-form-item>
                </a-col>
                <a-col :span="7" v-if="isWeChatMassSelected">
                  <a-form-item :field="'reachChannel'" label="触达渠道" :rules="[{ required: true, message: '请选择触达渠道' }]" >
                    
                      <a-select v-model="entity.reachChannel" placeholder="请选择触达渠道">
                        <a-option v-for="item in reachChannelOptions" :key="item" :value="item" :label="item" />
                      </a-select>
                    
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <!-- <a-col v-if="templateType.value !== 'TENCENT_WORK'" :span="24"> -->
                  <a-form-item :label="t('reach.sms.templateContent')" field="setting.template">
                    <a-textarea v-model="entity.content"  :auto-size="{ minRows: 2, maxRows: 10 }" />
                  </a-form-item>

                  <!-- 企业微信消息类型：根据模板的多种pushType显示对应表单 -->
                  <template v-if="entity.flowNodeId === 'wechat_enterprise' && selectedPushTypes.length > 0">
                    <!-- 图片类型 -->
                    <template v-if="selectedPushTypes.includes('image')">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="图片" :field="'imageId'" :rules="[{ required: true, message: '请选择图片' }]">
                            <a-select v-model="entity.imageId" allow-search placeholder="请选择图片" @focus="loadImageMaterials">
                              <a-option 
                                v-for="it in imageMaterials" 
                                :key="it.id" 
                                :value="it.id" 
                                :label="it.name"
                                :disabled="!['jpg','png'].includes((it.name.split('.').pop() || '').toLowerCase())"
                              />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" style="display:flex;align-items:center;">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('image')" :disabled="!entity.imageId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 视频类型 -->
                    <template v-if="selectedPushTypes.includes('video')">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="视频" :field="'videoId'" :rules="[{ required: true, message: '请选择视频' }]">
                            <a-select v-model="entity.videoId" allow-search placeholder="请选择视频" @focus="loadVideoMaterials">
                              <a-option
                                v-for="it in videoMaterials"
                                :key="it.id"
                                :value="it.id"
                                :label="it.name"
                                :disabled="(it.name.split('.').pop() || '').toLowerCase() !== 'mp4'"
                              />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" style="display:flex;align-items:center;">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('video')" :disabled="!entity.videoId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 文件类型 -->
                    <template v-if="selectedPushTypes.includes('file')">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="文件" :field="'fileId'" :rules="[{ required: true, message: '请选择文件' }]">
                            <a-select v-model="entity.fileId" allow-search placeholder="请选择文件" @focus="loadFileMaterials">
                              <a-option
                                v-for="it in fileMaterials"
                                :key="it.id"
                                :value="it.id"
                                :label="it.name"
                                :disabled="!['txt','pdf','doc','docx','ppt','pptx','xls','xlsx','xml','jpg','jpeg','png','bmp','gif']
                                  .includes((it.name.split('.').pop() || '').toLowerCase())"
                              />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" style="display:flex;align-items:center;">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('file')" :disabled="!entity.fileId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 小程序类型 -->
                    <template v-if="selectedPushTypes.includes('miniprogram')">
                      <a-row :gutter="24">
                        <a-col :span="7">
                          <a-form-item label="消息标题" :field="'miniprogramTitle'" :rules="[{ required: true, message: '请输入消息标题' }]">
                            <a-input v-model="entity.miniprogramTitle" placeholder="请输入消息标题" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="7">
                          <a-form-item label="appid" :field="'miniprogramAppid'" :rules="[{ required: true, message: '请输入appid' }]">
                            <a-input v-model="entity.miniprogramAppid" placeholder="请输入小程序appid" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="7">
                          <a-form-item label="封面图片" :field="'miniprogramCoverId'" :rules="[{ required: true, message: '请选择封面图片' }]">
                            <a-select v-model="entity.miniprogramCoverId" allow-search placeholder="请选择封面图片" @focus="loadImageMaterials">
                              <a-option v-for="it in imageMaterials" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="2">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('miniprogramCover')" :disabled="!entity.miniprogramCoverId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                      <a-row :gutter="24">
                        <a-col :span="24">
                          <a-form-item label="页面路径" :field="'miniprogramPagePath'" :rules="[{ required: true, message: '请输入页面路径' }]">
                            <a-input v-model="entity.miniprogramPagePath" placeholder="请输入小程序页面路径" />
                          </a-form-item>
                        </a-col> 
                      </a-row>
                    </template>

                    <!-- 链接类型 -->
                    <template v-if="selectedPushTypes.includes('link')">
                      <a-row :gutter="24">
                        <a-col :span="7">
                          <a-form-item label="链接标题" :field="'linkTitle'" :rules="[{ required: true, message: '请输入链接标题' }]">
                            <a-input v-model="entity.linkTitle" placeholder="请输入链接标题" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="7">
                          <a-form-item label="链接描述" :field="'linkDescription'" >
                            <a-input v-model="entity.linkDescription" placeholder="请输入链接描述" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="7">
                          <a-form-item label="封面图片" :field="'linkCoverId'" :rules="[{ required: true, message: '请选择封面图片' }]">
                            <a-select v-model="entity.linkCoverId" allow-search placeholder="请选择封面图片" @focus="loadImageMaterials">
                              <a-option v-for="it in imageMaterials" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="2">
                          <div class="m-t-p1">
                            <a-button type="text" size="mini" @click="previewSelected('linkCover')" :disabled="!entity.linkCoverId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                      <a-row :gutter="24">
                        <a-col :span="24">
                          <a-form-item label="链接URL" :field="'linkUrl'" :rules="[{ required: true, message: '请输入链接URL' }]">
                            <a-input v-model="entity.linkUrl" placeholder="请输入链接URL" />
                          </a-form-item>
                        </a-col>
                      </a-row>
                    </template>
                  </template>

                  <!-- 微信群发消息类型：保持原有逻辑 -->
                  <template v-else>
                    <!-- 图片类型：选择图片 + 图片标题 + 推荐语 + 已选图片列表 -->
                    <template v-if="selectedPushType === 'image'">
                      <a-row :gutter="24">
                        <a-col :span="8">
                          <a-form-item :field="'imageIds'" label="图片" :rules="[{ required: true, message: '请选择图片' }]">
                            <a-select 
                              v-model="entity.imageIds" 
                              multiple 
                              allow-search 
                              placeholder="请选择图片" 
                              @focus="loadImageMaterials"
                              @change="onImageSelectChange"
                            >
                              <a-option 
                                v-for="it in imageMaterials" 
                                :key="it.id" 
                                :value="it.id" 
                                :label="it.name" 
                                :disabled="isImageOptionDisabled(it.id)"
                              />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="8">
                          <a-form-item label="图片标题" field="imageTitle">
                            <a-input v-model="entity.imageTitle" placeholder="请输入图片标题" />
                          </a-form-item>
                        </a-col>
                        <a-col :span="8">
                          <a-form-item label="推荐语" field="recommendText">
                            <a-input v-model="entity.recommendText" placeholder="请输入推荐语" />
                          </a-form-item>
                        </a-col>
                      </a-row>
                      <a-form-item label="已选图片">
                        <div>
                          <div v-if="selectedImages.length === 0" style="color:#999;">暂无已选图片</div>
                          <div v-else>
                            <div v-for="img in selectedImages" :key="img.id" style="display:flex; align-items:center; height:32px;">
                              <span style="flex:1;">{{ img.name }}</span>
                              <a-button type="text" size="mini" @click="previewUrl(img.url)">预览</a-button>
                              <a-button type="text" size="mini" status="danger" @click="removeImage(img.id)">删除</a-button>
                            </div>
                          </div>
                        </div>
                      </a-form-item>
                    </template>

                    <!-- 音频类型：选择音频 + 预览按钮 -->
                    <template v-if="selectedPushType === 'audio'">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="音频" :field="'audioId'" :rules="[{ required: true, message: '请选择音频' }]">
                            <a-select v-model="entity.audioId" allow-search placeholder="请选择音频" @focus="loadAudioMaterials">
                              <a-option v-for="it in audioMaterials" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" style="display:flex;align-items:center;">
                          <!-- <a-button @click="previewSelected('audio')">预览</a-button> -->
                          <div class="m-t-p1">
                          <a-button type="text" size="mini" @click="previewSelected('audio')" :disabled="!entity.audioId">预览</a-button>
                          </div>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 视频类型：选择视频 + 预览按钮 -->
                    <template v-if="selectedPushType === 'video'">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="视频" :field="'videoId'" :rules="[{ required: true, message: '请选择视频' }]">
                            <a-select v-model="entity.videoId" allow-search placeholder="请选择视频" @focus="loadVideoMaterials">
                              <a-option v-for="it in videoMaterials" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12" >
                          <!-- <a-button @click="previewSelected('video')">预览</a-button> -->
                          <a-button type="text" size="mini" @click="previewSelected('video')" :disabled="!entity.videoId">预览</a-button>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 图文类型：选择图文 + 允许转载 -->
                    <template v-if="selectedPushType === 'article'">
                      <a-row :gutter="24">
                        <a-col :span="12">
                          <a-form-item label="图文" :field="'articleId'" :rules="[{ required: true, message: '请选择图文' }]">
                            <a-select v-model="entity.articleId" allow-search placeholder="请选择图文" @focus="loadArticleList">
                              <a-option v-for="it in articleList" :key="it.id" :value="it.id" :label="it.name" />
                            </a-select>
                          </a-form-item>
                        </a-col>
                        <a-col :span="12">
                          <a-form-item label="允许转载" field="allowReprint">
                            <a-radio-group v-model="entity.allowReprint">
                              <a-radio :value="true">是</a-radio>
                              <a-radio :value="false">否</a-radio>
                            </a-radio-group>
                          </a-form-item>
                        </a-col>
                      </a-row>
                    </template>

                    <!-- 卡券类型：选择卡券 -->
                    <template v-if="selectedPushType === 'coupon'">
                      <a-form-item label="卡券" :field="'couponId'" :rules="[{ required: true, message: '请选择卡券' }]">
                        <a-select v-model="entity.couponId" allow-search placeholder="请选择卡券" @focus="loadCouponList">
                          <a-option v-for="it in couponList" :key="it.id" :value="it.id" :label="it.name" />
                        </a-select>
                      </a-form-item>
                    </template>
                  </template>
                  <a-form-item :label="t('reach.sms.description')" field="summary">
                    <a-textarea v-model="entity.summary" :placeholder="t('reach.reminder.description')" />
                  </a-form-item>
                </a-col>
                <!-- <a-col :span="24">
                  <a-typography-title :heading="6">
                    {{ t('reach.sms.fieldMappingConfiguration') }}
                    <a-tooltip :content="t('reach.sms.syncTemplateField')">
                      <icon-sync class="sync-fields" @click="refreshFields" />
                    </a-tooltip>
                  </a-typography-title>
                  <MappingTemplate v-model:dataList="entity.mapping" :is-change="true"
                    :customer-model="customerModel" />
                </a-col> -->
              </a-row>
            </a-card>
          </a-space>
        </a-form>

        <!-- 显示 -->
        <!-- <ModelView :entity="entity" :view-type="viewType.value" /> -->
      </div>
    </template>

    <template #action>
      <!-- <a-button v-if="module.isEdit" type="primary" :loading="simulating" @click="simulate">模拟发送</a-button> -->
      <simulate-dialog ref="simulateDlg" />
    </template>
  </module>
</template>

<script>
import { ref, provide, computed, getCurrentInstance, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getMaterialList } from "@/api/material";
import { findBenefitList } from "@/api/benefit";
import { createContent, modifyContent, getContent } from "@/api/flow-content";
import { Message } from "@arco-design/web-vue";
import { useMenuItemState } from "@/store";
import { formatFields } from "@/utils/field";
import { getDynamicFields } from "@/utils/content";
import { findCustomerModel } from "@/api/system";
import { findTemplateList } from "@/api/flow";
import { findNodeLists } from "@/api/node";
import SimulateDialog from "@/components/modal-dlg/customer-selector-dlg.vue";
import MappingTemplate from "@/components/ma/mapping-template/index.vue";
import ModelView from "@/components/ma/model-view/index.vue";

export default {
  components: {
    SimulateDialog,
    MappingTemplate,
    ModelView,
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();
    const menuItem = useMenuItemState();
    menuItem.loadFromLocalStorage();
    const queryValue = route.query.id;
    const customerModel = ref({});
    const viewType = ref("SMS");

    const module = ref({
      entityIdField: "id",
      mainPath: menuItem.path,
      breadcrumb: [
        {
          // name: `${menuItem.meta.title || ""}沟通列表`,
          name: `${menuItem.meta.title || ''} ${t("reach.title.communicationList")}`,
          path: menuItem.path,
        },
        {
          // name: `编辑${menuItem.meta.title}`,
          name: `${t("global.button.edit")} ${menuItem.meta.title}`,
        },
      ],
      isEdit: !!queryValue,
    });

    const loading = ref(false);
    const entity = ref({
      setting: {},
      mapping: [],
      content: "",
      category: menuItem.meta.id,
      reachChannel: "",
      imageIds: [],
      allowReprint: false,
      // 企业微信消息相关字段
      imageId: "",
      videoId: "",
      fileId: "",
      miniprogramTitle: "",
      miniprogramAppid: "",
      miniprogramCoverId: "",
      miniprogramPagePath: "",
      linkTitle: "",
      linkDescription: "",
      linkCoverId: "",
      linkUrl: "",
    });
    const formRef = ref({});
    const simulating = ref(false);

    const flows = ref([]);
    const starts = ref([]);
    const nodeList = ref([]);
    const templateList = ref([]);
    const templateType = ref();
    const reachChannelOptions = ref([
      "环游嘉年华公众号",
      "华立电竞",
      "卡片嘉年华",
      "太鼓鼓众广场",
      "华立科技",
    ]);

    // 物料/卡券等下拉数据
    const imageMaterials = ref([]);
    const audioMaterials = ref([]);
    const videoMaterials = ref([]);
    const fileMaterials = ref([]);
    const articleList = ref([]);
    const couponList = ref([]);

    viewType.value = computed(() => {
      const item = nodeList.value.find((x) => {
        return x.id === entity.value.flowNodeId;
      });
      return item?.setting?.templateType || "SMS";
    });
    templateType.value = computed(() => {
      const item = nodeList.value[0]
      return item.setting.templateType;
    });
    const getFLowList = (flowNodeId = "", type = "change") => {
      const params = {};
      if (flowNodeId) {
        params.expression = `nodeConfigId eq ${flowNodeId}`;
      }

      findTemplateList(params).then((res) => {
        if (type === "change") {
          entity.value.flowTemplateId = "";
          entity.value.content = "";
          entity.value.mapping = [];
          entity.value.reachChannel = "";
        }
        const list = Array.isArray(res) ? res : [];
        // 基于名称关键字为模板打上类型标记，便于前端联动
        const mapWithType = list.map((it) => {
          const name = it?.name || "";
          let pushType = it?.pushType || it?.type || "";
          if (!pushType) {
            if (/卡券|coupon/i.test(name)) pushType = "coupon";
            else if (/图文|article/i.test(name)) pushType = "article";
            else if (/图片|图像|pic|image/i.test(name)) pushType = "image";
            else if (/音频|audio/i.test(name)) pushType = "audio";
            else if (/视频|video/i.test(name)) pushType = "video";
          }
          return { ...it, pushType };
        });
        // 若后端暂无数据，提供前端演示用的占位模板
        templateList.value = mapWithType.length > 0 ? mapWithType : [
          { id: 'tpl_img_demo', name: '图片消息-活动通知 [demo_img]', pushType: 'image', content: '' },
          { id: 'tpl_audio_demo', name: '音频消息-活动通知 [demo_audio]', pushType: 'audio', content: '' },
          { id: 'tpl_video_demo', name: '视频消息-活动通知 [demo_video]', pushType: 'video', content: '' },
          { id: 'tpl_article_demo', name: '图文消息-活动通知 [demo_article]', pushType: 'article', content: '' },
          { id: 'tpl_coupon_demo', name: '卡券消息-活动通知 [demo_coupon]', pushType: 'coupon', content: '' },
        ];

        // 查询当前模板
        changeTemplate(entity.value.flowTemplateId, "init");
      });
    };

    const changeTemplate = (id, type = "change") => {
      const template = templateList.value.find((x) => {
        return x.id === id;
      });

      if (!template) {
        return false;
      }
      entity.value.content = template.content;
      
      // 企业微信消息：支持多种pushType
      if (entity.value.flowNodeId === 'wechat_enterprise') {
        // 基于模板名称识别包含的多种类型
        const name = template?.name || '';
        const pushTypes = [];
        
        if (/图片|图像|pic|image/i.test(name)) pushTypes.push('image');
        if (/视频|video/i.test(name)) pushTypes.push('video');
        if (/文件|file/i.test(name)) pushTypes.push('file');
        if (/小程序|miniprogram|mini/i.test(name)) pushTypes.push('miniprogram');
        if (/链接|link|url/i.test(name)) pushTypes.push('link');
        
        selectedPushTypes.value = pushTypes;
      } else {
        // 其他消息类型：保持原有单一pushType逻辑
        let pushType = template?.pushType || template?.type || '';
        if (!pushType && template?.name) {
          const name = template.name;
          if (/卡券|coupon/i.test(name)) pushType = 'coupon';
          else if (/图文|article/i.test(name)) pushType = 'article';
          else if (/图片|图像|pic|image/i.test(name)) pushType = 'image';
          else if (/音频|audio/i.test(name)) pushType = 'audio';
          else if (/视频|video/i.test(name)) pushType = 'video';
        }
        selectedPushType.value = pushType;
        
        // 当选择图文类型时，默认允许转载为"是"
        if (pushType === 'article') {
          entity.value.allowReprint = true;
        }
      }
      
      if (template?.variables && template.variables.length > 0) {
        let content = "";
        template.variables.forEach((item) => {
          if (item.type === "image") {
            content += `<img style="width: 100%" src="${item.value}">`;
          }
          if (item.type === "text") {
            content += `<div class="content-text">${item.value}</div>`;
          }
        });
        entity.value.viewContent = content;
      } else {
        entity.value.viewContent = template.content;
      }

      if (entity.value.mapping.length === 0 || type === "change") {
        refreshFields();
      }
    };

    const refreshFields = () => {
      entity.value.mapping.length = 0;
      entity.value.mapping.push(...getDynamicFields(entity.value.content));
    };

    const bindData = async () => {
      if (module.value.isEdit) {
        const res = await getContent(queryValue);
        entity.value = {
          ...entity.value,
          ...res,
          mapping: res.mapping || [],
          imageIds: res.imageIds || [],
          allowReprint: res.allowReprint || false,
          // 企业微信消息相关字段
          imageId: res.imageId || "",
          videoId: res.videoId || "",
          fileId: res.fileId || "",
          miniprogramTitle: res.miniprogramTitle || "",
          miniprogramAppid: res.miniprogramAppid || "",
          miniprogramCoverId: res.miniprogramCoverId || "",
          miniprogramPagePath: res.miniprogramPagePath || "",
          linkTitle: res.linkTitle || "",
          linkDescription: res.linkDescription || "",
          linkCoverId: res.linkCoverId || "",
          linkUrl: res.linkUrl || "",
        };
      }

      const data = await findCustomerModel();
      if (data) {
        customerModel.value = data;
        customerModel.value.fields = formatFields(data.fields);
      }

      const params = {};
      if (menuItem.meta.id) {
        params.expression = `flowCategory eq ${menuItem.meta.id}`;
      }

      nodeList.value = await findNodeLists(params);
      await getFLowList(entity.value.flowNodeId, "init");

      // entity.value.mapping = ;
    };


    const simulate = async () => {
      const customers = await simulateDlg.value.show(customerModel.value);
      if (customers) {
        simulating.value = true;
        try {
          const customer = customers[0];
          const simResult = {};
          // await simulateCommunicateSend({
          //   capabilityId: entity.value.id,
          //   customerId: customer,
          //   triggerType: 'simulate',
          //   setting: {
          //     limited: false
          //   },
          //   budgetSetting: {
          //     enabled: false
          //   },
          // });
          if (simResult.status !== "SUCCESS") {
            Message.error({
              content: `客户${simResult.customerId}模拟失败：${simResult.message}`,
              closable: true
            });
            return;
          }
          Message.success("模拟发送成功！");
        } finally {
          simulating.value = false;
        }
      }
    };

    const save = async () => {
      try {
        const result = await formRef.value.validate();
        if (result) {
          console.log('表单校验未通过:', result);
          return; // 校验失败时直接返回，不执行保存操作
        }
      } catch (error) {
        console.log('表单校验未通过:', error);
        return; // 校验失败时直接返回，不执行保存操作
      }
      
      if (module.value.isEdit) {
        await modifyContent(entity.value);
      } else {
        await createContent(entity.value);
      }

      Message.success(t('global.tips.success.save'));
      router.push(menuItem.path); // 保存成功后重定向回主页面
    };

    const isWeChatMassSelected = computed(() => {
      return entity.value.flowNodeId === 'wechat_group';
    });

    const selectedPushType = ref("");
    const selectedPushTypes = ref([]); // 企业微信消息支持多种类型
    const selectedImages = computed(() => {
      if (!entity.value.imageIds || !Array.isArray(entity.value.imageIds)) return [];
      return imageMaterials.value.filter((it) => entity.value.imageIds.includes(it.id));
    });

    const loadImageMaterials = async () => {
      if (imageMaterials.value.length > 0) return;
      const res = await getMaterialList({ page: 1, size: 50, type: 'image' });
      imageMaterials.value = res?.data?.list || res?.list || [];
    };
    const loadAudioMaterials = async () => {
      if (audioMaterials.value.length > 0) return;
      const res = await getMaterialList({ page: 1, size: 50, type: 'audio' });
      audioMaterials.value = res?.data?.list || res?.list || [];
    };
    const loadVideoMaterials = async () => {
      if (videoMaterials.value.length > 0) return;
      const res = await getMaterialList({ page: 1, size: 50, type: 'video' });
      videoMaterials.value = res?.data?.list || res?.list || [];
    };
    const loadFileMaterials = async () => {
      if (fileMaterials.value.length > 0) return;
      const res = await getMaterialList({ page: 1, size: 50, type: 'file' });
      fileMaterials.value = res?.data?.list || res?.list || [];
    };
    const loadArticleList = async () => {
      if (articleList.value.length > 0) return;
      const res = await getMaterialList({ page: 1, size: 50, type: 'file' });
      articleList.value = res?.data?.list || res?.list || [];
    };
    const loadCouponList = async () => {
      if (couponList.value.length > 0) return;
      const res = await findBenefitList({ fields: 'name', expression: 'type eq coupon AND status eq ENABLED' });
      couponList.value = res || [];
    };

    const previewSelected = (kind) => {
      let item;
      if (kind === 'audio') item = audioMaterials.value.find((x) => x.id === entity.value.audioId);
      if (kind === 'video') item = videoMaterials.value.find((x) => x.id === entity.value.videoId);
      if (kind === 'image') item = imageMaterials.value.find((x) => x.id === entity.value.imageId);
      if (kind === 'file') item = fileMaterials.value.find((x) => x.id === entity.value.fileId);
      if (kind === 'miniprogramCover') item = imageMaterials.value.find((x) => x.id === entity.value.miniprogramCoverId);
      if (kind === 'linkCover') item = imageMaterials.value.find((x) => x.id === entity.value.linkCoverId);
      if (item?.url) previewUrl(item.url);
    };

    const previewUrl = (url) => {
      if (!url) return;
      window.open(url, '_blank');
    };
    const removeImage = (id) => {
      if (!Array.isArray(entity.value.imageIds)) return;
      entity.value.imageIds = entity.value.imageIds.filter((x) => x !== id);
    };

    const onImageSelectChange = (value) => {
      if (value && value.length > 5) {
        // 保留前5个选项
        entity.value.imageIds = value.slice(0, 5);
        Message.warning("最多选择5张图片，请取消勾选已添加的图片。");
      }
    };

    const isImageOptionDisabled = (imageId) => {
      // 如果已选择的图片数量达到5张，且当前图片未被选中，则禁用该选项
      return entity.value.imageIds && 
             entity.value.imageIds.length >= 5 && 
             !entity.value.imageIds.includes(imageId);
    };

    const setup = {
      t,
      save,
      route,
      router,
      module,
      entity,
      formRef,
      templateList,
      bindData,
      nodeList,
      getFLowList,
      changeTemplate,
      refreshFields,
      viewType,
      customerModel,
      loading,
      flows,
      starts,
      templateType,
      reachChannelOptions,
      isWeChatMassSelected,
      selectedPushType,
      selectedPushTypes,
      imageMaterials,
      audioMaterials,
      videoMaterials,
      fileMaterials,
      articleList,
      couponList,
      selectedImages,
      loadImageMaterials,
      loadAudioMaterials,
      loadVideoMaterials,
      loadFileMaterials,
      loadArticleList,
      loadCouponList,
      previewSelected,
      previewUrl,
      removeImage,
      onImageSelectChange,
      isImageOptionDisabled,
    };
    provide("edit", setup);
    onMounted(async () => {
      await bindData();
    });
    return setup;
  },
};
</script>

<style lang="less" scoped>
.template-body {
  display: flex;
  height: calc(100vh - 160px);
  background-color: #ffffff;

  .left-form {
    overflow-x: hidden;
    overflow-y: scroll;
  }
}

::v-deep(.general-card) {
  min-height: calc(100vh - 160px);
}

.sync-fields {
  cursor: pointer;
  color: rgb(var(--primary-6));
}

.m-t-p {
  position: relative;
  width: 100%;
  margin-top: 22px;
}
.m-t-p1 {
  position: relative;
  width: 100%;
  margin-top: 35px;
}
</style>