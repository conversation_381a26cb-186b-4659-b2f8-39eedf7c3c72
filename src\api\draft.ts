import axios from 'axios';
import type { HttpResponse } from './interceptor';

// 请求参数接口
export interface DraftInfoDTO {
  wechatOfficialAccountCode: string; // 微信公众号编码: funLoopLand - 环游， esports - 华立电竞， card - 卡片嘉年华
  offset?: number; // 偏移量，默认从 0 开始
  count?: number; // 获取数量，默认 10 条数据
}

// 图文内容接口
export interface NewsItem {
  article_type: string; // 文章类型
  title: string; // 标题
  author: string; // 作者
  digest: string; // 说明
  url: string; // 临时地址
}

export interface DraftContent {
  news_item: NewsItem[];
}

// 草稿项接口
export interface DraftItem {
  media_id: string; // 文章唯一标识
  title: string; // 文章标题
  content: DraftContent;
}

// 响应数据接口
export interface DraftInfoData {
  total_count: number; // 总条数
  item_count: number; // 当前页数据条数
  item: DraftItem[];
}

export interface DraftInfoResponse {
  msg: string;
  code: number;
  data: DraftInfoData;
}

// 获取草稿信息（图文）
export function getDraftInfo(params: DraftInfoDTO) {
  return axios.post<HttpResponse<DraftInfoData>>('/api/wechat/official/accounts/getDraftInfo', params, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
