<template>
  <div class="ma-campaign-list">
    <a-table ref="table" v-model:selectedKeys="selectedKeys" row-key="id" :bordered="false" :data="campaignList"
      :pagination="false" @sorter-change="sorterChangeItem">
      <template #columns>
        <a-table-column :title="t('campaign.column.canvasId')" data-index="id" :sortable="{ sortDirections: ['ascend', 'descend'] }"
          :width="60" />
        <a-table-column :title="t('campaign.column.canvasName')" data-index="name" :ellipsis="true" :tooltip="true"
          :sortable="{ sortDirections: ['ascend', 'descend'] }" :width="60" />
        <a-table-column :title="t('campaign.column.category')" data-index="categoryName" :width="30" />
        <a-table-column :title="t('campaign.column.type')" data-index="type" :width="30">
          <template #cell="{ record }">
            {{ filters(campaignType, record.type) }}
          </template>
        </a-table-column>

        <!-- <a-table-column title="人群" data-index="audienceName" :width="30" />

        <a-table-column title="事件" data-index="eventCode" :width="30" />

        <a-table-column title="能力类型" data-index="capabilityType" :width="30">
          <template #cell="{ record }">
            {{ filters(capabilityType, record.capabilityType, "title", "type") }}
          </template>
        </a-table-column>
        <a-table-column title="能力名称" data-index="capabilityName" :width="40" /> -->

        <!-- <a-table-column title="已发送" data-index="type" :width="30" /> -->

        <a-table-column :title="t('campaign.column.tags')" data-index="tags" :width="50">

          <template #cell="{ record }">
            <div v-if="record.tags?.length > 0" class="cell-item">
              <a-tag style="margin-right: 5px">{{ record.tags[0] }}</a-tag>
              <a-popover>
                <a-tag v-if="record.tags.length > 1">···</a-tag>
                <template #content>
                  <a-space wrap>
                    <template v-for="(item, index) in record.tags" :key="item">
                      <template v-if="index > 0">
                        <a-tag class="tag-item">{{ item }}</a-tag>
                      </template>
                    </template>
                  </a-space>
                </template>
              </a-popover>
            </div>
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.column.status')" align="center" data-index="status" :width="30">

          <template #cell="{ record }">
            <span class="status" :class="record.status">{{
      filterStatus(record.status)
    }}</span>
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.column.description')" data-index="summary" :width="50" :ellipsis="true"
          :tooltip="{ class: 'tooltip-content' }" />
        <a-table-column :title="t('campaign.column.startTime')" data-index="startTime" :sortable="{ sortDirections: ['ascend', 'descend'] }"
          :width="30">

          <template #cell="{ record }">
            {{ $moment(record.startTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.column.endTime')" data-index="endTime" :sortable="{ sortDirections: ['ascend', 'descend'] }"
          :width="30">

          <template #cell="{ record }">
            {{ $moment(record.endTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </a-table-column>
        <a-table-column :title="t('campaign.column.action')" align="center" :width="60">

          <template #cell="{ record }">
            <a-link v-if="record.type !== 'FLOW' && editEnabled(record.status)" v-permission="['ma_menu.campaign.design']" @click="main.modifyTask(record)">{{t('global.button.edit')}}</a-link>
            <a-link v-if="record.type == 'FLOW' && editEnabled(record.status)" v-permission="['ma_menu.campaign.design']" @click="main.modifyTask(record)">{{t('global.button.design')}}</a-link>
            <a-link v-if="record.type == 'AUDIENCE' && !editEnabled(record.status)" v-permission="['ma_menu.campaign.view']" @click="main.viewAudienceCampaign(record)">{{t('global.button.view')}}</a-link>
            <a-link v-if="record.type == 'FLOW'" v-permission="['ma_menu.campaign.view']" @click="main.viewFlow(record)">{{t('global.button.view')}}</a-link>
            <a-link v-if="record.status === 'DRAFT' || record.status === 'REJECTED'" v-permission="['ma_menu.campaign.commit']" @click="commit(record)">{{t('global.button.submit')}}</a-link>
            <a-link v-if="record.status === 'COMMITTED'" v-permission="['ma_menu.campaign.approve']" @click="approveTask(record.id)">{{t('global.button.approval')}}</a-link>
            <a-link v-if="record.status === 'APPROVED'" v-permission="['ma_menu.campaign.start']" @click="startTask(record.id)">{{t('global.button.start')}}</a-link>
            <a-link v-if="record.status === 'RUNNING'" v-permission="['ma_menu.campaign.pause']" @click="pauseTask(record.id)">{{t('global.button.pause')}}</a-link>
            <a-link v-if="record.status === 'PAUSED'" v-permission="['ma_menu.campaign.resume']" @click="resumeTask(record.id)">{{t('global.button.continueAction')}}</a-link>
            <a-link v-if="record.status === 'RUNNING' || record.status === 'PAUSED'" v-permission="['ma_menu.campaign.stop']" @click="stopTask(record.id)">{{t('global.button.stop')}}</a-link>

            <a-dropdown>
              <a-button size="mini" type="text">
                <IconMore />
              </a-button>
              <template #content>
                <a-doption v-permission="['ma_menu.campaign.save-as']" @click="saveAs(record.id)">{{t('global.button.saveAs')}}</a-doption>
                <!-- <a-doption @click="main.showBudget(record)">预算</a-doption> -->
                <a-doption v-permission="['ma_menu.campaign.optlog']" @click="showRecord(record)">{{t('global.button.operationRecord')}}</a-doption>
                <template v-if="record.started">
                  <a-doption v-permission="['ma_menu.campaign.report']" @click="main.showReport(record)">{{t('global.button.report')}}</a-doption>
                </template>

                <template v-if="!record.started">
                  <a-doption v-permission="['ma_menu.campaign.delete']" @click="deleteCampaign(record)">{{t('global.button.delete')}}</a-doption>
                </template>
              </template>
            </a-dropdown>
          </template>
        </a-table-column>
      </template>
    </a-table>

    <a-modal v-model:visible="commitModel" @ok="commitTask">

      <template #title> {{t('campaign.flow.taskCommit')}}</template>
      <task-commit ref="taskCommitRef" />
    </a-modal>
    <a-modal v-model:visible="recordModel" width="60%" :footer="false">

      <template #title> {{t('campaign.flow.taskRecord')}}</template>
      <task-record ref="recordRef" />
    </a-modal>
    <task-approve-dlg ref="taskApproveRef" />
    <CloneTaskDlg ref="saveAsRef" />
  </div>
</template>

<script setup>
import { ref, inject, defineProps, reactive, getCurrentInstance } from "vue";
import { useBussinessUnitStore } from "@/store";
import { campaignType, campaignStatus } from "@/constant/campaign";
import { capabilityType } from "@/constant/capability";
import { filters } from "@/utils/filter";
import {
  commitCampaign,
  startCampaign,
  modifyCampaign,
  pauseCampaign,
  resumeCampaign,
  stopCampaign
} from "@/api/campaign";
import { Modal, Notification } from "@arco-design/web-vue";
import TaskCommit from "./task-commit.vue";
import TaskApproveDlg from "./task-approve-dlg.vue";
import TaskRecord from "./task-record.vue";
import taskEditDrawer from "./task-edit-drawer.vue";
import CloneTaskDlg from "./clone-task-dlg.vue";

const {
      proxy: { t }
    } = getCurrentInstance();

const owner = useBussinessUnitStore().marketingCenter;

const { campaignList, handleModifyTask, sorterChange } = defineProps([
  "campaignList",
  "handleModifyTask",
  "sorterChange"
]);
const main = inject("main");

const filterStatus = (status) => {
  const item = campaignStatus.find((item) => {
    return item.value === status;
  });
  return item.label || "--";
};

const commitModel = ref(false);
const taskCommitRef = ref(null);
const taskApproveRef = ref(null);
const saveAsRef = ref(null);

const campaign = ref(null);
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: "checkbox",
  width: 10,
  showCheckedAll: true,
  onlyCurrent: false
});

const refreshTask = async () => {
  main.refresh();
};

const saveAs = async (id) => {
  await saveAsRef.value.show(id);
  refreshTask();
};

const editEnabled = (status) => {
  return owner?.setting?.customizationSetting?.campaignEditableStatus.indexOf(
    status
  ) !== -1
}

const commit = (task) => {
  campaign.value = JSON.parse(JSON.stringify(task));
  commitModel.value = true;
};

/**
 * 提交活动
 */
const commitTask = async () => {
  const data = taskCommitRef.value.getData();
  data.campaignId = campaign.value.id;
  data.timestamp = new Date();
  await commitCampaign(data);
  refreshTask();
};
/**
 * 审批活动
 */
const approveTask = async (campaignId) => {
  await taskApproveRef.value.show(campaignId);
  refreshTask();
};

const handleSaveTask = async (task) => {
  if (task.id) {
    await modifyCampaign(task);
  }
  Notification.info({
    title: "保存营销活动",
    content: "营销活动保存成功。"
  });
  refreshTask();
};
const startTask = async (id) => {
  Modal.confirm({
    title: "启动营销活动",
    content: "是否确认启动营销活动？",
    onOk: async () => {
      await startCampaign(id);
      refreshTask();
    }
  });
};

/**
 * 删除活动
 */
const deleteCampaign = (item) => {
  Modal.confirm({
    title: "删除营销活动",
    content: `是否确认删除营销活动 ${item.name}?`,
    onOk: () => {
      main.deleteTask(item);
    }
  });
};

const pauseTask = async (id) => {
  Modal.confirm({
    title: "暂停营销活动",
    content: "是否确认暂停营销活动？",
    onOk: async () => {
      await pauseCampaign(id);
      refreshTask();
    }
  });
};
const resumeTask = async (id) => {
  Modal.confirm({
    title: "恢复营销活动",
    content: "是否确认恢复营销活动？",
    onOk: async () => {
      await resumeCampaign(id);
      refreshTask();
    }
  });
};

const stopTask = async (id) => {
  Modal.confirm({
    title: "停止营销活动",
    content: "停止后将不能再重新启动, 是否确认停止营销活动？",
    onOk: async () => {
      await stopCampaign(id);
      refreshTask();
    }
  });
};

// 切换数据
const sorterChangeItem = (dataIndex, direction) => {
  sorterChange(`${dataIndex},${direction === "ascend" ? "ASC" : "DESC"}`);
};

// 显示操作记录
const recordModel = ref(false);
const recordRef = ref(null);

const showRecord = (item) => {
  recordModel.value = true;
  recordRef.value.activeId = item.id;
  recordRef.value.dataList = [];
  recordRef.value.getDataList();
};
</script>

<style lang="less" scoped>
.ma-campaign-list {
  .tag-item {
    margin: 0 5px 5px 0;
  }

  .status {
    &.DRAFT {
      color: rgba(var(--green-6));
    }

    &.COMMITTED {
      color: rgba(var(--lime-6));
    }

    &.APPROVED {
      color: rgba(var(--cyan-6));
    }

    &.REJECTED {
      color: rgba(var(--gold-6));
    }

    &.RUNNING {
      color: rgba(var(--blue-6));
    }

    &.PAUSED {
      color: rgba(var(--orange-6));
    }

    &.FINISHED {
      color: rgba(var(--arcoblue-6));
    }

    &.STOP {
      color: rgba(var(--gray-6));
    }
  }

  .cell-item {
    display: flex;
    align-items: center;
  }
}
</style>
