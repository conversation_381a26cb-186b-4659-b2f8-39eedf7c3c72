<template>
  <module edit>
    <template #action>
      <a-button
        v-if="owner?.setting?.customizationSetting?.aiEnable == 'ENABLED'"
        type="primary"
        @click="showAiDlg"
        >{{t('reach.title.aiGenerated')}}</a-button
      >
    </template>
    <template #main>
      <a-form
        ref="formRef"
        layout="vertical"
        class="general-form"
        :model="entity"
        :disabled="!entity.own"
      >
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <a-row :gutter="80">
              <a-col :span="6">
                <a-form-item
                  field="id"
                  :label="t('reach.column.templateCode')"
                  :rules="[{ required: true, message: t('reach.reminder.templateCode') }]"
                  label-col-flex="70px"
                >
                  <a-input
                    v-model="entity.id"
                    :placeholder="t('reach.reminder.templateCode')"
                    :disabled="module.isEdit"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item
                  field="name"
                  :label="t('reach.column.templateName')"
                  :rules="[{ required: true, message: t('reach.reminder.templateName') }]"
                  label-col-flex="70px"
                >
                  <a-input v-model="entity.name" :placeholder="t('reach.reminder.templateName')" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item
                  field="nodeConfigId"
                  :label="t('reach.column.touchpoint')"
                  :rules="[{ required: true, message: t('reach.reminder.touchpoint') }]"
                  label-col-flex="70px"
                >
                  <a-select
                    v-model="entity.nodeConfigId"
                    :disabled="module.isEdit"
                    :placeholder="t('reach.reminder.touchpoint')"
                    @change="selectNodeConfig"
                  >
                    <a-option
                      v-for="item of nodeConfigs"
                      :key="item.id"
                      :value="item.id"
                      >{{ item.name }}</a-option>
                    <a-option value="sms">三方短信</a-option>
                                    <a-option value="wechat_enterprise">企业微信消息</a-option>
                                    <a-option value="wechat_group">微信群发消息</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item
                  v-if="['wechat_enterprise', 'wechat_group'].includes(nodeTemplateType)"
                  field="pushType"
                  label="推送类型"
                  :rules="[{ required: true, message: '请选择推送类型' }]"
                  label-col-flex="70px"
                >
                  <a-select
                    v-model="entity.pushType"
                    :options="pushTypeOptions"
                    :multiple="nodeTemplateType === 'wechat_enterprise'"
                    :disabled="module.isEdit"
                    placeholder="请选择推送类型"
                    allow-clear
                    
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="80">
              <a-col>
                <!-- <a-col v-if="nodeTemplateType !== 'TENCENT_WORK'"> -->
                <a-form-item
                  field="content"
                  :label="t('reach.column.templateContent')"
                  label-col-flex="70px"
                >
                  <a-textarea
                    v-model="entity.content"
                    type="textarea"
                    :placeholder="t('reach.reminder.templateContent')"
                    :auto-size="{ minRows: 2, maxRows: 10 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="80">
              <a-col :span="8">
                <a-form-item field="sign" :label="t('reach.column.category')" label-col-flex="70px">
                  <a-input v-model="entity.group" :placeholder="t('reach.reminder.category')" />
                </a-form-item>
              </a-col>
              <a-col v-if="nodeTemplateType !== 'TENCENT_WORK'" :span="8">
                <a-form-item field="sign" :label="t('reach.column.signature')" label-col-flex="70px">
                  <a-input v-model="entity.sign" :placeholder="t('reach.reminder.signature')" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" :label="t('reach.column.status')" label-col-flex="70px">
                  <a-switch
                    v-model="entity.status"
                    type="round"
                    checked-value="ENABLED"
                    unchecked-value="DISABLED"
                  >
                    <template #checked> {{t('global.button.enable')}} </template>
                    <template #unchecked> {{t('global.button.disable')}} </template>
                  </a-switch>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="80">
              <a-col :span="24">
                <a-form-item field="summary" :label="t('reach.edit.remark')" label-col-flex="70px">
                  <a-textarea
                    v-model="entity.summary"
                    type="textarea"
                    :placeholder="t('reach.reminder.remark')"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-space>
      </a-form>
      <a-form layout="vertical" class="general-form" :model="entity">
        <a-space direction="vertical" :size="16">
          <a-card class="general-card">
            <limit-setting
              v-model:limitSetting="entity.limitSetting"
              :model-fields="modelFields"
              :disabled="false"
            />
          </a-card>
        </a-space>
      </a-form>
      <AiDlg ref="aiDlgRef"></AiDlg>
    </template>
  </module>
</template>

<script>
import { computed, ref, provide, getCurrentInstance } from 'vue'
import { useRouter, useRoute } from "vue-router";
import { createTemplate, modifyTemplate, getTemplate } from "@/api/flow";
import { findNodeList } from "@/api/node";
import { Message } from "@arco-design/web-vue";
import { useBussinessUnitStore } from "@/store";
import { findCustomerModel } from "@/api/system";
import { formatFields } from "@/utils/field";
import LimitSetting from "@/components/ma/limit-setting/index.vue";
import AiDlg from "@/components/modal-dlg/ai-dlg.vue";

export default {
  components: {
    AiDlg,
    LimitSetting,
  },
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const owner = useBussinessUnitStore().marketingCenter;
    const route = useRoute();
    const router = useRouter();
    const queryValue = route.query.id;

    const module = ref({
      entityIdField: "id",
      mainPath: "/reach/flow-template",
      breadcrumb: [
        {
          // name: "触达模板管理",
          name: t('reach.title.reachTemplateManagement'),
          path: "/reach/flow-template",
        },
        {
          // name: "编辑触达模板",
          name: t('reach.title.editReachTemplate'),
        },
      ],
      isEdit: !!queryValue,
    });
    const loading = ref(false);
    const entity = ref({
      own: true,
      status: "DISABLED",
      limitSetting: {
        type: "DISABLED",
        dateLimit: {},
      },
    });
    const formRef = ref({});
    const nodeConfigs = ref([]);
    const aiDlgRef = ref(null);
    const modelFields = ref();
    const nodeTemplateType = ref();
    const getModelFields = async () => {
      const data = await findCustomerModel();
      // console.log(data);
      if (data) {
        modelFields.value = formatFields(data.fields);
      }
      // console.log(modelFields.value);
    };

    const getNodeConfigList = async () => {
      const res = await findNodeList();
      res.map((item) => {
        if (item.category !== "SYSTEM") {
          nodeConfigs.value.push({
            id: item.id,
            name: item.name,
            setting: item.setting,
          });
        }
        return [];
      });
    };

    const bindData = async () => {
      await getNodeConfigList();
      await getModelFields();
      if (module.value.isEdit) {
        try {
          loading.value = true;
          const res = await getTemplate(queryValue);
          entity.value = res;
          const found = nodeConfigs.value.find(
            (item) => item.id === entity.value.nodeConfigId
          );
          // 优先使用节点配置中的模板类型；若未找到，则兼容三方短信/企业微信/微信群发的直传值
          nodeTemplateType.value = found?.setting?.templateType ||
            (['wechat_enterprise', 'wechat_group', 'sms'].includes(entity.value.nodeConfigId)
              ? entity.value.nodeConfigId
              : undefined);
          // 统一 limitSetting 结构
          entity.value.limitSetting = entity.value.limitSetting || { type: "DISABLED", dateLimit: {} };
          // 规范化 pushType，以便多选/单选正确展示
          if (nodeTemplateType.value === 'wechat_enterprise') {
            if (!Array.isArray(entity.value.pushType)) {
              entity.value.pushType = entity.value.pushType ? [entity.value.pushType] : [];
            }
          } else if (nodeTemplateType.value === 'wechat_group') {
            if (Array.isArray(entity.value.pushType)) {
              entity.value.pushType = entity.value.pushType[0] || '';
            }
          }
        } finally {
          loading.value = false;
        }
      } else {
        // 新增模式初始化
        entity.value = {
          own: true,
          status: "DISABLED",
          limitSetting: { type: "DISABLED", dateLimit: {} }
        };
      }
    };
    const quit = () => {
      router.push({ path: module.value.mainPath });
    };
    const save = async () => {
      formRef.value.validate();
      if (module.value.isEdit) {
        await modifyTemplate(entity.value);
      } else {
        await createTemplate(entity.value);
      }
      // Message.success("保存成功！");
      Message.success(t('global.tips.success.save'));
      quit();
    };
    // 新建弹窗
    const showAiDlg = () => {
      aiDlgRef.value.show();
    };
    // 选择触点
    const selectNodeConfig = (sel) => {
      nodeTemplateType.value = sel;
      entity.value.pushType = sel === 'wechat_enterprise' ? [] : null;
    };
    
    const pushTypeOptions = computed(() => {
  if (nodeTemplateType.value === 'wechat_enterprise') {
    return [
      { label: '图片', value: 'image' },
      { label: '链接', value: 'link' },
      { label: '小程序', value: 'miniprogram' },
      { label: '视频', value: 'video' },
      { label: '文件', value: 'file' }
    ]
  } else {
    return [
      { label: '文本', value: 'text' },
      { label: '图片', value: 'image' },
      { label: '图文', value: 'article' },
      { label: '音频', value: 'audio' },
      { label: '视频', value: 'video' },
      { label: '卡券', value: 'coupon' }
    ]
  }
});
    const setup = {
      t,
      save,
      route,
      router,
      module,
      entity,
      formRef,
      bindData,
      loading,
      nodeConfigs,
      showAiDlg,
      aiDlgRef,
      modelFields,
      owner,
      selectNodeConfig,
      nodeTemplateType,
      pushTypeOptions
    };
    provide("edit", setup);
    return setup;
  },
};
</script>